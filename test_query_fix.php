<?php
// Test the database query fix
require_once 'system/config/config.php';
require_once 'system/classes/database.class.php';

try {
    // Test the query building without actually executing it
    $db = database::table('email_campaign_history as ech')
        ->select([
            'ech.*',
            'u.name as triggered_by_name'
        ])
        ->leftJoin('autobooks_users as u', 'ech.triggered_by_user', '=', 'u.id')
        ->where('ech.campaign_id', 2)
        ->orderBy('ech.created_at', 'desc')
        ->limit(100);
    
    // Use reflection to access the private buildQuery method
    $reflection = new ReflectionClass($db);
    $buildQueryMethod = $reflection->getMethod('buildQuery');
    $buildQueryMethod->setAccessible(true);
    
    $query = $buildQueryMethod->invoke($db);
    echo "Generated Query:\n";
    echo $query . "\n\n";
    
    // Also test the bindings
    $getBindingsMethod = $reflection->getMethod('getBindings');
    $getBindingsMethod->setAccessible(true);
    
    $bindings = $getBindingsMethod->invoke($db);
    echo "Generated Bindings:\n";
    print_r($bindings);
    
    echo "\nQuery should now have 'ech.`campaign_id`' instead of '`ech.campaign_id`'\n";
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
