<?php
namespace system;

use system\database;
use system\Schema;
use PDO;
use Exception;

/**
 * Data Source Manager Class
 * 
 * Manages database data sources for tables and email campaigns
 * Provides UI-driven configuration and data retrieval
 */
class data_source_manager {
    
    private static $log_target = "data_source_manager";
    
    /**
     * Get all available database tables that can be used as data sources
     * 
     * @return array List of available tables with metadata
     */
    public static function get_available_tables(): array {
        try {
            // Get all tables in the database
            $tables_query = "SHOW TABLES";
            $stmt = database::rawQuery($tables_query);
            $all_tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $available_tables = [];
            
            foreach ($all_tables as $table_name) {
                // Skip system tables and temporary tables
                if (strpos($table_name, 'temp_') === 0 ||
                    strpos($table_name, 'cache_') === 0 ||
                    in_array($table_name, ['sessions', 'migrations', 'information_schema', 'mysql', 'performance_schema', 'sys'])) {
                    continue;
                }

                // Get table info with error handling
                try {
                    $table_info = self::get_table_info($table_name);
                    if ($table_info) {
                        $available_tables[] = $table_info;
                    }
                } catch (Exception $e) {
                    tcs_log("Error getting info for table $table_name: " . $e->getMessage(), self::$log_target);
                    // Continue with other tables
                    continue;
                }
            }
            
            // Sort by table name
            usort($available_tables, function($a, $b) {
                return strcmp($a['name'], $b['name']);
            });
            
            return $available_tables;
            
        } catch (Exception $e) {
            tcs_log("Error getting available tables: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Get detailed information about a specific table
     * 
     * @param string $table_name Table name
     * @return array|null Table information
     */
    public static function get_table_info(string $table_name): ?array {
        try {
            // Get column information
            $columns_query = "DESCRIBE `$table_name`";
            $stmt = database::rawQuery($columns_query);
            $columns = $stmt->fetchAll(PDO::FETCH_ASSOC);

            // Get row count
            $count_query = "SELECT COUNT(*) as row_count FROM `$table_name`";
            $stmt = database::rawQuery($count_query);
            $row_count = $stmt->fetch(PDO::FETCH_ASSOC)['row_count'];
            
            // Determine table type and category
            $category = self::categorize_table($table_name, $columns);
            
            return [
                'name' => $table_name,
                'display_name' => self::format_table_display_name($table_name),
                'category' => $category,
                'row_count' => (int)$row_count,
                'columns' => $columns,
                'primary_key' => self::get_primary_key($columns),
                'has_data_json' => self::has_data_json_column($columns),
                'created_at' => self::get_created_at_column($columns),
                'description' => self::generate_table_description($table_name, $columns, $row_count)
            ];
            
        } catch (Exception $e) {
            tcs_log("Error getting table info for $table_name: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }
    
    /**
     * Get sample data from a table for preview
     * 
     * @param string $table_name Table name
     * @param int $limit Number of rows to return
     * @return array Sample data
     */
    public static function get_sample_data(string $table_name, int $limit = 5): array {
        try {
            $db = database::table($table_name);
            $data = $db->limit($limit)->get();
            
            return [
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ];
            
        } catch (Exception $e) {
            tcs_log("Error getting sample data for $table_name: " . $e->getMessage(), self::$log_target);
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [],
                'count' => 0
            ];
        }
    }
    
    /**
     * Create a new data source configuration
     * 
     * @param array $config Data source configuration
     * @return int Data source ID
     */
    public static function create_data_source(array $config): int {
        try {
            // Ensure data source table exists
            self::ensure_data_source_table();
            
            $defaults = [
                'status' => 'active',
                'created_by' => $_SESSION['user_id'] ?? null,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];
            
            $data_source = array_merge($defaults, $config);
            
            // Validate required fields
            if (empty($data_source['name']) || empty($data_source['table_name'])) {
                throw new Exception('Data source name and table name are required');
            }
            
            // Convert arrays to JSON
            if (isset($data_source['column_mapping']) && is_array($data_source['column_mapping'])) {
                $data_source['column_mapping'] = json_encode($data_source['column_mapping']);
            }

            if (isset($data_source['filters']) && is_array($data_source['filters'])) {
                $data_source['filters'] = json_encode($data_source['filters']);
            }

            if (isset($data_source['tables']) && is_array($data_source['tables'])) {
                $data_source['tables'] = json_encode($data_source['tables']);
            }

            if (isset($data_source['joins']) && is_array($data_source['joins'])) {
                $data_source['joins'] = json_encode($data_source['joins']);
            }

            if (isset($data_source['selected_columns']) && is_array($data_source['selected_columns'])) {
                $data_source['selected_columns'] = json_encode($data_source['selected_columns']);
            }

            if (isset($data_source['table_aliases']) && is_array($data_source['table_aliases'])) {
                $data_source['table_aliases'] = json_encode($data_source['table_aliases']);
            }

            if (isset($data_source['column_aliases']) && is_array($data_source['column_aliases'])) {
                $data_source['column_aliases'] = json_encode($data_source['column_aliases']);
            }

            if (isset($data_source['custom_columns']) && is_array($data_source['custom_columns'])) {
                $data_source['custom_columns'] = json_encode($data_source['custom_columns']);
            }

            if (isset($data_source['sorting']) && is_array($data_source['sorting'])) {
                $data_source['sorting'] = json_encode($data_source['sorting']);
            }

            if (isset($data_source['grouping']) && is_array($data_source['grouping'])) {
                $data_source['grouping'] = json_encode($data_source['grouping']);
            }

            if (isset($data_source['limits']) && is_array($data_source['limits'])) {
                $data_source['limits'] = json_encode($data_source['limits']);
            }
            
            $db = database::table('autobooks_data_sources');
            $result = $db->insert($data_source);
            
            tcs_log("Created data source: " . $data_source['name'], self::$log_target);
            return $result;
            
        } catch (Exception $e) {
            tcs_log("Error creating data source: " . $e->getMessage(), self::$log_target);
            throw $e;
        }
    }
    
    /**
     * Get all configured data sources
     *
     * @param array $criteria Optional filtering criteria
     * @return array List of data sources
     */
    public static function get_data_sources(array $criteria = []): array {
        try {
            self::ensure_data_source_table();

            $db = database::table('autobooks_data_sources');

            // Apply campaign filtering if specified
            if (!empty($criteria['campaign_id'])) {
                // For now, we'll get the campaign's assigned data source
                // In the future, this could be expanded to support multiple data sources per campaign
                $campaign_data_source = database::table('email_campaigns')
                    ->where('id', $criteria['campaign_id'])
                    ->value('data_source_id');

                if ($campaign_data_source) {
                    $db->where('id', $campaign_data_source);
                } else {
                    // If no data source is assigned to the campaign, return all sources
                    // This allows for initial selection during campaign setup
                }
            }

            $sources = $db->orderBy('name')->get();
            
            // Decode JSON fields
            foreach ($sources as &$source) {
                if (!empty($source['column_mapping'])) {
                    $source['column_mapping'] = json_decode($source['column_mapping'], true);
                }
                if (!empty($source['filters'])) {
                    $source['filters'] = json_decode($source['filters'], true);
                }
                if (!empty($source['tables'])) {
                    $source['tables'] = json_decode($source['tables'], true);
                }
                if (!empty($source['joins'])) {
                    $source['joins'] = json_decode($source['joins'], true);
                }
                if (!empty($source['selected_columns'])) {
                    $source['selected_columns'] = json_decode($source['selected_columns'], true);
                }
                if (!empty($source['table_aliases'])) {
                    $source['table_aliases'] = json_decode($source['table_aliases'], true);
                }
                if (!empty($source['column_aliases'])) {
                    $source['column_aliases'] = json_decode($source['column_aliases'], true);
                }
                if (!empty($source['custom_columns'])) {
                    $source['custom_columns'] = json_decode($source['custom_columns'], true);
                }
                if (!empty($source['sorting'])) {
                    $source['sorting'] = json_decode($source['sorting'], true);
                }
                if (!empty($source['grouping'])) {
                    $source['grouping'] = json_decode($source['grouping'], true);
                }
                if (!empty($source['limits'])) {
                    $source['limits'] = json_decode($source['limits'], true);
                }
            }
            
            return $sources;
            
        } catch (Exception $e) {
            tcs_log("Error getting data sources: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }
    
    /**
     * Get data from a configured data source
     * 
     * @param int $data_source_id Data source ID
     * @param array $criteria Additional criteria (search, limit, etc.)
     * @return array Data results
     */
    public static function get_data_source_data(int $data_source_id, array $criteria = []): array {
        try {
            // Get data source configuration
            $data_source = self::get_data_source($data_source_id);
            if (!$data_source) {
                throw new Exception('Data source not found');
            }
            
            $db = database::table($data_source['table_name']);
            
            // Apply filters from data source configuration
            if (!empty($data_source['filters'])) {
                foreach ($data_source['filters'] as $filter) {
                    $db->where($filter['column'], $filter['operator'] ?? '=', $filter['value']);
                }
            }
            
            // Apply additional criteria
            if (!empty($criteria['search'])) {
                // Search across all text columns
                $table_info = self::get_table_info($data_source['table_name']);
                $searchable_columns = [];
                
                foreach ($table_info['columns'] as $column) {
                    if (strpos($column['Type'], 'varchar') !== false || 
                        strpos($column['Type'], 'text') !== false) {
                        $searchable_columns[] = $column['Field'];
                    }
                }
                
                if (!empty($searchable_columns)) {
                    $db->where(function($query) use ($searchable_columns, $criteria) {
                        foreach ($searchable_columns as $column) {
                            $query->orWhere($column, 'LIKE', '%' . $criteria['search'] . '%');
                        }
                    });
                }
            }
            
            // Apply sorting
            if (!empty($criteria['sort_column'])) {
                $direction = $criteria['sort_direction'] ?? 'asc';
                $db->orderBy($criteria['sort_column'], $direction);
            }
            
            // Apply pagination
            if (!empty($criteria['limit'])) {
                $db->limit($criteria['limit']);
                if (!empty($criteria['offset'])) {
                    $db->offset($criteria['offset']);
                }
            }
            
            $data = $db->get();
            
            return [
                'success' => true,
                'data' => $data,
                'count' => count($data),
                'data_source' => $data_source
            ];
            
        } catch (Exception $e) {
            tcs_log("Error getting data source data: " . $e->getMessage(), self::$log_target);
            return [
                'success' => false,
                'error' => $e->getMessage(),
                'data' => [],
                'count' => 0
            ];
        }
    }
    
    /**
     * Get a specific data source by ID
     * 
     * @param int $id Data source ID
     * @return array|null Data source configuration
     */
    public static function get_data_source(int $id): ?array {
        try {
            self::ensure_data_source_table();
            
            $db = database::table('autobooks_data_sources');
            $source = $db->where('id', $id)->first();
            
            if ($source) {
                // Decode JSON fields
                if (!empty($source['column_mapping'])) {
                    $source['column_mapping'] = json_decode($source['column_mapping'], true);
                }
                if (!empty($source['filters'])) {
                    $source['filters'] = json_decode($source['filters'], true);
                }
                if (!empty($source['tables'])) {
                    $source['tables'] = json_decode($source['tables'], true);
                }
                if (!empty($source['joins'])) {
                    $source['joins'] = json_decode($source['joins'], true);
                }
                if (!empty($source['selected_columns'])) {
                    $source['selected_columns'] = json_decode($source['selected_columns'], true);
                }
                if (!empty($source['table_aliases'])) {
                    $source['table_aliases'] = json_decode($source['table_aliases'], true);
                }
                if (!empty($source['column_aliases'])) {
                    $source['column_aliases'] = json_decode($source['column_aliases'], true);
                }
                if (!empty($source['custom_columns'])) {
                    $source['custom_columns'] = json_decode($source['custom_columns'], true);
                }
                if (!empty($source['sorting'])) {
                    $source['sorting'] = json_decode($source['sorting'], true);
                }
                if (!empty($source['grouping'])) {
                    $source['grouping'] = json_decode($source['grouping'], true);
                }
                if (!empty($source['limits'])) {
                    $source['limits'] = json_decode($source['limits'], true);
                }
            }
            
            return $source;
            
        } catch (Exception $e) {
            tcs_log("Error getting data source $id: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }
    
    // Private helper methods
    
    private static function categorize_table(string $table_name, array $columns): string {
        if (strpos($table_name, 'autobooks_') === 0) {
            if (strpos($table_name, '_data') !== false) {
                return 'data_table';
            } elseif (strpos($table_name, 'email') !== false || strpos($table_name, 'campaign') !== false) {
                return 'email';
            } elseif (strpos($table_name, 'user') !== false) {
                return 'users';
            } else {
                return 'system';
            }
        } elseif (strpos($table_name, 'autodesk') === 0) {
            return 'autodesk';
        } else {
            return 'other';
        }
    }
    
    private static function format_table_display_name(string $table_name): string {
        // Remove common prefixes
        $display_name = $table_name; //str_replace(['autobooks_', 'autodesk_'], '', $table_name);
        
        // Replace underscores with spaces and capitalize
        $display_name = ucwords(str_replace('_', ' ', $display_name));
        
        return $display_name;
    }
    
    private static function get_primary_key(array $columns): ?string {
        foreach ($columns as $column) {
            if ($column['Key'] === 'PRI') {
                return $column['Field'];
            }
        }
        return null;
    }
    
    private static function has_data_json_column(array $columns): bool {
        foreach ($columns as $column) {
            if ($column['Field'] === 'data_json') {
                return true;
            }
        }
        return false;
    }
    
    private static function get_created_at_column(array $columns): ?string {
        foreach ($columns as $column) {
            if (in_array($column['Field'], ['created_at', 'date_created', 'timestamp'])) {
                return $column['Field'];
            }
        }
        return null;
    }
    
    private static function generate_table_description(string $table_name, array $columns, int $row_count): string {
        $category = self::categorize_table($table_name, $columns);
        $column_count = count($columns);
        
        $descriptions = [
            'data_table' => "Data table with $column_count columns and $row_count records",
            'email' => "Email/campaign table with $column_count columns and $row_count records", 
            'users' => "User management table with $column_count columns and $row_count records",
            'system' => "System table with $column_count columns and $row_count records",
            'autodesk' => "Autodesk integration table with $column_count columns and $row_count records",
            'other' => "Database table with $column_count columns and $row_count records"
        ];
        
        return $descriptions[$category] ?? $descriptions['other'];
    }
    
    private static function ensure_data_source_table(): void {
        // Check if table exists using raw query for better compatibility
        try {
            $check_query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'";
            $stmt = database::rawQuery($check_query);
            $exists = $stmt->fetchColumn() > 0;

            if (!$exists) {
                // Create table using raw SQL for maximum compatibility
                $create_sql = "
                CREATE TABLE `autobooks_data_sources` (
                    `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
                    `name` varchar(255) NOT NULL COMMENT 'Display name for the data source',
                    `table_name` varchar(255) NOT NULL COMMENT 'Primary database table name',
                    `description` text DEFAULT NULL COMMENT 'Optional description',
                    `category` varchar(50) NOT NULL DEFAULT 'other' COMMENT 'Data source category',
                    `tables` longtext DEFAULT NULL COMMENT 'JSON array of all tables used in this data source',
                    `joins` longtext DEFAULT NULL COMMENT 'JSON array of join configurations',
                    `selected_columns` longtext DEFAULT NULL COMMENT 'JSON array of selected columns from all tables',
                    `table_aliases` longtext DEFAULT NULL COMMENT 'JSON object mapping table names to their aliases',
                    `column_aliases` longtext DEFAULT NULL COMMENT 'JSON object mapping column keys to their aliases',
                    `custom_columns` longtext DEFAULT NULL COMMENT 'JSON array of custom SQL columns with aliases',
                    `sorting` longtext DEFAULT NULL COMMENT 'JSON array of sorting rules (ORDER BY)',
                    `grouping` longtext DEFAULT NULL COMMENT 'JSON array of grouping rules (GROUP BY)',
                    `limits` longtext DEFAULT NULL COMMENT 'JSON object with limit and offset settings',
                    `column_mapping` longtext DEFAULT NULL COMMENT 'JSON column mapping configuration',
                    `filters` longtext DEFAULT NULL COMMENT 'JSON filter configuration',
                    `status` varchar(20) NOT NULL DEFAULT 'active' COMMENT 'Data source status',
                    `created_by` int(11) DEFAULT NULL COMMENT 'User ID who created this data source',
                    `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
                    `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
                    PRIMARY KEY (`id`),
                    KEY `idx_status_category` (`status`,`category`),
                    KEY `idx_table_name` (`table_name`)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='Data source configurations for tables and email campaigns with multi-table support'
                ";

                database::rawQuery($create_sql);
                tcs_log("Created autobooks_data_sources table using raw SQL", self::$log_target);
            } else {
                // Table exists, check for missing columns and add them
                self::ensure_data_source_columns();
            }
        } catch (Exception $e) {
            tcs_log("Error ensuring autobooks_data_sources table: " . $e->getMessage(), self::$log_target);
            // Don't throw - let the system continue without the table for now
        }
    }

    /**
     * Ensure all required columns exist in the data source table
     */
    private static function ensure_data_source_columns(): void {
        try {
            // Define required columns with their definitions
            $required_columns = [
                'tables' => 'longtext DEFAULT NULL COMMENT \'JSON array of all tables used in this data source\'',
                'joins' => 'longtext DEFAULT NULL COMMENT \'JSON array of join configurations\'',
                'table_aliases' => 'longtext DEFAULT NULL COMMENT \'JSON object mapping table names to their aliases\'',
                'column_aliases' => 'longtext DEFAULT NULL COMMENT \'JSON object mapping column keys to their aliases\'',
                'custom_columns' => 'longtext DEFAULT NULL COMMENT \'JSON array of custom SQL columns with aliases\'',
                'sorting' => 'longtext DEFAULT NULL COMMENT \'JSON array of sorting rules (ORDER BY)\'',
                'grouping' => 'longtext DEFAULT NULL COMMENT \'JSON array of grouping rules (GROUP BY)\'',
                'limits' => 'longtext DEFAULT NULL COMMENT \'JSON object with limit and offset settings\''
            ];

            // Check which columns exist
            $existing_columns_query = "SELECT COLUMN_NAME FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'autobooks_data_sources'";
            $stmt = database::rawQuery($existing_columns_query);
            $existing_columns = $stmt->fetchAll(PDO::FETCH_COLUMN);

            // Add missing columns
            foreach ($required_columns as $column_name => $column_definition) {
                if (!in_array($column_name, $existing_columns)) {
                    $alter_sql = "ALTER TABLE `autobooks_data_sources` ADD COLUMN `{$column_name}` {$column_definition}";
                    database::rawQuery($alter_sql);
                    tcs_log("Added missing column '{$column_name}' to autobooks_data_sources table", self::$log_target);
                }
            }
        } catch (Exception $e) {
            tcs_log("Error ensuring data source columns: " . $e->getMessage(), self::$log_target);
            // Don't throw - let the system continue
        }
    }
}
