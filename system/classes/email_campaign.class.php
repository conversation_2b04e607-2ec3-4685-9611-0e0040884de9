<?php

namespace system;

use system\database;
use PDO;
use DateTime;
use Exception;

/**
 * Email Campaign Management System
 * 
 * Handles creation, management, and execution of email campaigns
 * Integrates with existing email infrastructure and database patterns
 */
class email_campaign {

    private array $placeholders = [];

    public function __construct() {
        // Database class is used statically, no need to store instance
    }
    
    /**
     * Create a new email campaign
     * 
     * @param array $data Campaign data
     * @return int Campaign ID
     */
    public function create_campaign(array $data): int {
        $defaults = [
            'status' => 'draft',
            'type' => 'general',
            'created_by' => $_SESSION['user_id'] ?? null,
            'total_sent' => 0,
            'total_delivered' => 0,
            'total_failed' => 0,
            'is_system' => 0
        ];
        
        $campaign_data = array_merge($defaults, $data);
        
        // Validate required fields
        if (empty($campaign_data['name'])) {
            throw new Exception('Campaign name is required');
        }

        // Validate data source if provided
        if (!empty($campaign_data['data_source_id'])) {
            $data_source = \system\data_source_manager::get_data_source($campaign_data['data_source_id']);
            if (!$data_source) {
                throw new Exception('Invalid data source selected');
            }
        }
        
        // Convert arrays to JSON
        if (isset($campaign_data['target_audience']) && is_array($campaign_data['target_audience'])) {
            $campaign_data['target_audience'] = json_encode($campaign_data['target_audience']);
        }
        if (isset($campaign_data['send_rules']) && is_array($campaign_data['send_rules'])) {
            $campaign_data['send_rules'] = json_encode($campaign_data['send_rules']);
        }
        if (isset($campaign_data['send_schedule']) && is_array($campaign_data['send_schedule'])) {
            $campaign_data['send_schedule'] = json_encode($campaign_data['send_schedule']);
        }
        
        return database::table('email_campaigns')->insert($campaign_data);
    }
    
    /**
     * Get campaign by ID
     * 
     * @param int $id Campaign ID
     * @return array|null Campaign data
     */
    public function get_campaign(int $id): ?array {
        $campaign = database::table('email_campaigns')
            ->select(['*'])
            ->where('id', $id)
            ->first();
            
        if ($campaign) {
            // Decode JSON fields
            $campaign['target_audience'] = json_decode($campaign['target_audience'] ?? '{}', true);
            $campaign['send_rules'] = json_decode($campaign['send_rules'] ?? '{}', true);
            $campaign['send_schedule'] = json_decode($campaign['send_schedule'] ?? '{}', true);
        }
        
        return $campaign;
    }
    
    /**
     * Get all campaigns with optional filtering
     * 
     * @param array $criteria Filter criteria
     * @return array Campaigns list
     */
    public function get_campaigns(array $criteria = []): array {
        $query = database::table('email_campaigns as ec')
            ->select([
                'ec.*',
                'u.name as created_by_name'
            ])
            ->leftJoin('autobooks_users as u', 'ec.created_by', '=', 'u.id')
            ->orderBy('ec.updated_at', 'desc');
            
        // Apply filters
        if (isset($criteria['status'])) {
            $query->where('ec.status', $criteria['status']);
        }
        if (isset($criteria['type'])) {
            $query->where('ec.type', $criteria['type']);
        }
        if (isset($criteria['is_system'])) {
            $query->where('ec.is_system', $criteria['is_system']);
        }
        if (isset($criteria['search'])) {
            $search = '%' . $criteria['search'] . '%';
            // Since the database class doesn't support closures, we'll use a simpler approach
            // This will search in name field - we can extend this later if needed
            $query->where('ec.name', 'LIKE', $search);
        }
        
        $campaigns = $query->get();
        print_rr($campaigns,'get_campaigns');
        // Decode JSON fields for each campaign
        foreach ($campaigns as &$campaign) {
            $campaign['target_audience'] = json_decode($campaign['target_audience'] ?? '{}', true);
            $campaign['send_rules'] = json_decode($campaign['send_rules'] ?? '{}', true);
            $campaign['send_schedule'] = json_decode($campaign['send_schedule'] ?? '{}', true);
        }
        
        return $campaigns;
    }
    
    /**
     * Update campaign
     * 
     * @param int $id Campaign ID
     * @param array $data Update data
     * @return bool Success status
     */
    public function update_campaign(int $id, array $data): bool {
        // Convert arrays to JSON
        if (isset($data['target_audience']) && is_array($data['target_audience'])) {
            $data['target_audience'] = json_encode($data['target_audience']);
        }
        if (isset($data['send_rules']) && is_array($data['send_rules'])) {
            $data['send_rules'] = json_encode($data['send_rules']);
        }
        if (isset($data['send_schedule']) && is_array($data['send_schedule'])) {
            $data['send_schedule'] = json_encode($data['send_schedule']);
        }
        
        return database::table('email_campaigns')
            ->where('id', $id)
            ->update($data) > 0;
    }
    
    /**
     * Delete campaign and related data
     * 
     * @param int $id Campaign ID
     * @return bool Success status
     */
    public function delete_campaign(int $id): bool {
        // Check if it's a system campaign
        $campaign = $this->get_campaign($id);
        if ($campaign && $campaign['is_system']) {
            throw new Exception('Cannot delete system campaigns');
        }
        
        return database::table('email_campaigns')
            ->where('id', $id)
            ->delete() > 0;
    }
    
    /**
     * Create or update campaign template
     * 
     * @param int $campaign_id Campaign ID
     * @param array $template_data Template data
     * @return int Template ID
     */
    public function save_template(int $campaign_id, array $template_data): int {
        $defaults = [
            'campaign_id' => $campaign_id,
            'version' => 1,
            'template_type' => 'html',
            'is_active' => 1,
            'created_by' => $_SESSION['user_id'] ?? null
        ];
        
        $data = array_merge($defaults, $template_data);
        
        // Convert placeholders array to JSON
        if (isset($data['placeholders']) && is_array($data['placeholders'])) {
            $data['placeholders'] = json_encode($data['placeholders']);
        }
        
        // Check if template exists for this campaign
        $existing = database::table('email_campaign_templates')
            ->where('campaign_id', $campaign_id)
            ->where('is_active', 1)
            ->first();

        if ($existing) {
            // Update existing template
            $data['version'] = $existing['version'] + 1;

            // Deactivate old template
            database::table('email_campaign_templates')
                ->where('id', $existing['id'])
                ->update(['is_active' => 0]);
        }

        return database::table('email_campaign_templates')->insert($data);
    }
    
    /**
     * Get active template for campaign
     *
     * @param int $campaign_id Campaign ID
     * @return array|null Template data
     */
    public function get_template(int $campaign_id): ?array {
        // First, try to get template from new system
        $template = database::table('email_campaign_templates')
            ->where('campaign_id', $campaign_id)
            ->where('is_active', 1)
            ->orderBy('version', 'desc')
            ->first();

        if ($template && $template['placeholders']) {
            $template['placeholders'] = json_decode($template['placeholders'], true);
        }

        // If no template found in new system, check for legacy email_template
        if (!$template) {
            $campaign = database::table('email_campaigns')
                ->select(['email_template', 'subject_template'])
                ->where('id', $campaign_id)
                ->first();

            if ($campaign && !empty($campaign['email_template'])) {
                $template = $this->load_legacy_template($campaign['email_template'], $campaign['subject_template']);
            }
        }

        return $template;
    }

    /**
     * Load legacy template from email_template column
     *
     * @param string $template_file Template filename
     * @param string $subject_template Subject template
     * @return array|null Template data in new format
     */
    private function load_legacy_template(string $template_file, ?string $subject_template = null): ?array {
        // Build the full path to the template file
        $template_path = FS_APP_ROOT . '/resources/email_templates/' . $template_file;

        if (!file_exists($template_path)) {
            return null;
        }

        try {
            $template_content = file_get_contents($template_path);

            if ($template_content === false) {
                return null;
            }

            // Return template in the same format as the new system
            return [
                'id' => null, // Legacy templates don't have IDs
                'campaign_id' => null,
                'name' => 'Legacy Template: ' . $template_file,
                'version' => 1,
                'subject_template' => $subject_template,
                'body_template' => $template_content,
                'template_type' => 'html',
                'placeholders' => $this->extract_placeholders_from_template($template_content),
                'is_active' => 1,
                'created_by' => null,
                'created_at' => null,
                'updated_at' => null
            ];

        } catch (Exception $e) {
            // Log error but don't throw - just return null
            error_log("Error loading legacy template $template_file: " . $e->getMessage());
            return null;
        }
    }

    /**
     * Extract placeholders from template content
     *
     * @param string $template_content Template content
     * @return array Placeholders found in template
     */
    private function extract_placeholders_from_template(string $template_content): array {
        $placeholders = [];

        // Find all {{placeholder}} patterns
        if (preg_match_all('/\{\{([^}]+)\}\}/', $template_content, $matches)) {
            foreach ($matches[1] as $placeholder) {
                $placeholder = trim($placeholder);
                // Handle conditional placeholders like {{subs_status,"active":"is ending soon"}}
                $base_placeholder = explode(',', $placeholder)[0];
                $placeholders[$base_placeholder] = ucwords(str_replace('_', ' ', $base_placeholder));
            }
        }

        return $placeholders;
    }

    /**
     * Get available placeholders for a campaign based on its data source
     *
     * @param int $campaign_id Campaign ID
     * @return array Available placeholders with display names
     */
    public function get_campaign_placeholders(int $campaign_id): array {
        $campaign = $this->get_campaign($campaign_id);
        if (!$campaign || empty($campaign['data_source_id'])) {
            // Return default placeholders if no data source is configured
            return $this->get_default_placeholders();
        }

        return $this->get_data_source_placeholders($campaign['data_source_id']);
    }

    /**
     * Get placeholders from a data source
     *
     * @param int $data_source_id Data source ID
     * @return array Placeholders based on data source columns
     */
    public function get_data_source_placeholders(int $data_source_id): array {
        try {
            $data_source = \system\data_source_manager::get_data_source($data_source_id);
            if (!$data_source) {
                return $this->get_default_placeholders();
            }

            // Get table information to extract column names
            $table_info = \system\data_source_manager::get_table_info($data_source['table_name']);
            if (!$table_info || empty($table_info['columns'])) {
                return $this->get_default_placeholders();
            }

            $placeholders = [];
            foreach ($table_info['columns'] as $column) {
                $column_name = $column['Field'];

                // Skip system columns that aren't useful for email templates
                if (in_array($column_name, ['id', 'created_at', 'updated_at', 'deleted_at'])) {
                    continue;
                }

                // Create a user-friendly display name
                $display_name = ucwords(str_replace(['_', '-'], ' ', $column_name));
                $placeholders[$column_name] = $display_name;
            }

            return $placeholders;

        } catch (Exception $e) {
            tcs_log("Error getting data source placeholders: " . $e->getMessage(), "email_campaign");
            return $this->get_default_placeholders();
        }
    }

    /**
     * Get default placeholders when no data source is available
     *
     * @return array Default placeholder set
     */
    private function get_default_placeholders(): array {
        return [
            'endCustomerName' => 'End Customer Name',
            'product_name' => 'Product Name',
            'term' => 'Term',
            'seats' => 'Seats',
            'subscriptionReferenceNumber' => 'Subscription Reference Number',
            'opportunityNumber' => 'Opportunity Number',
            'endCustomerCsn' => 'End Customer CSN',
            'status' => 'Status',
            'startDate' => 'Start Date',
            'endDate' => 'End Date'
        ];
    }

    /**
     * Add recipients to campaign
     * 
     * @param int $campaign_id Campaign ID
     * @param array $recipients Recipients data
     * @return int Number of recipients added
     */
    public function add_recipients(int $campaign_id, array $recipients): int {
        $added = 0;
        
        foreach ($recipients as $recipient) {
            $data = [
                'campaign_id' => $campaign_id,
                'email' => $recipient['email'],
                'name' => $recipient['name'] ?? null,
                'recipient_type' => $recipient['type'] ?? 'manual',
                'reference_id' => $recipient['reference_id'] ?? null,
                'metadata' => isset($recipient['metadata']) ? json_encode($recipient['metadata']) : null
            ];
            
            try {
                database::table('email_campaign_recipients')->insert($data);
                $added++;
            } catch (Exception $e) {
                // Skip duplicates (unique constraint on campaign_id + email)
                continue;
            }
        }
        
        return $added;
    }
    
    /**
     * Get campaign recipients
     * 
     * @param int $campaign_id Campaign ID
     * @param array $criteria Filter criteria
     * @return array Recipients list
     */
    public function get_recipients(int $campaign_id, array $criteria = []): array {
        $query = database::table('email_campaign_recipients')
            ->where('campaign_id', $campaign_id)
            ->where('is_active', 1);
            
        if (isset($criteria['unsubscribed'])) {
            $query->where('unsubscribed', $criteria['unsubscribed']);
        }
        
        return $query->get();
    }
    
    /**
     * Record email send history
     *
     * @param array $history_data Send history data
     * @return int History record ID
     */
    public function record_send_history(array $history_data): int {
        $defaults = [
            'send_status' => 'pending',
            'triggered_by_user' => $_SESSION['user_id'] ?? null
        ];

        $data = array_merge($defaults, $history_data);

        // Execute the insert and get the last insert ID
        database::table('email_campaign_history')->insert($data);
        return (int) database::insertId();
    }
    
    /**
     * Update send history status
     * 
     * @param int $history_id History record ID
     * @param string $status New status
     * @param string|null $result Result message
     * @return bool Success status
     */
    public function update_send_status(int $history_id, string $status, ?string $result = null): bool {
        $data = ['send_status' => $status];
        
        if ($result !== null) {
            $data['send_result'] = $result;
        }
        
        // Set timestamp based on status
        switch ($status) {
            case 'sent':
                $data['sent_at'] = date('Y-m-d H:i:s');
                break;
            case 'delivered':
                $data['delivered_at'] = date('Y-m-d H:i:s');
                break;
        }
        
        return database::table('email_campaign_history')
            ->where('id', $history_id)
            ->update($data) > 0;
    }
    
    /**
     * Get campaign send history
     *
     * @param int $campaign_id Campaign ID
     * @param array $criteria Filter criteria
     * @return array Send history
     */
    public function get_send_history(int $campaign_id, array $criteria = []): array {
        $query = database::table('email_campaign_history as ech')
            ->select([
                'ech.*',
                'u.name as triggered_by_name'
            ])
            ->leftJoin('autobooks_users as u', 'ech.triggered_by_user', '=', 'u.id')
            ->where('ech.campaign_id', $campaign_id)
            ->orderBy('ech.created_at', 'desc');

        if (isset($criteria['status'])) {
            $query->where('ech.send_status', $criteria['status']);
        }

        if (isset($criteria['limit'])) {
            $query->limit($criteria['limit']);
        }

        return $query->get();
    }

    /**
     * Execute campaign - send emails based on campaign rules
     *
     * @param int $campaign_id Campaign ID
     * @param array $options Execution options
     * @return array Execution results
     */
    public function execute_campaign(int $campaign_id, array $options = []): array {
        $campaign = $this->get_campaign($campaign_id);
        if (!$campaign) {
            throw new Exception('Campaign not found');
        }

        if ($campaign['status'] !== 'active') {
            throw new Exception('Campaign is not active');
        }

        $template = $this->get_template($campaign_id);
        if (!$template) {
            throw new Exception('No active template found for campaign');
        }

        $results = [
            'total_processed' => 0,
            'total_sent' => 0,
            'total_failed' => 0,
            'errors' => []
        ];

        try {
            // Get target recipients based on campaign type
            $recipients = $this->get_campaign_recipients($campaign);
            $results['total_processed'] = count($recipients);

            // Send emails to each recipient
            foreach ($recipients as $recipient) {
                try {
                    $send_result = $this->send_campaign_email($campaign, $template, $recipient);
                    if ($send_result['success']) {
                        $results['total_sent']++;
                    } else {
                        $results['total_failed']++;
                        $results['errors'][] = $send_result['error'];
                    }
                } catch (Exception $e) {
                    $results['total_failed']++;
                    $results['errors'][] = $e->getMessage();
                }
            }

            // Update campaign statistics
            $this->update_campaign($campaign_id, [
                'total_sent' => $campaign['total_sent'] + $results['total_sent'],
                'total_failed' => $campaign['total_failed'] + $results['total_failed'],
                'last_run_at' => date('Y-m-d H:i:s')
            ]);

        } catch (Exception $e) {
            $results['errors'][] = $e->getMessage();
        }

        return $results;
    }

    /**
     * Get recipients for campaign based on data source
     *
     * @param array $campaign Campaign data
     * @return array Recipients list
     */
    private function get_campaign_recipients(array $campaign): array {
        // If no data source is configured, fall back to manual recipients
        if (empty($campaign['data_source_id'])) {
            return $this->get_data_source_recipients($campaign['id'], null);
        }

        return $this->get_data_source_recipients($campaign['id'], $campaign['data_source_id']);
    }

    /**
     * Get recipients from data source or manual recipients
     *
     * @param int $campaign_id Campaign ID
     * @param int|null $data_source_id Data source ID (null for manual recipients)
     * @return array Recipients list
     */
    private function get_data_source_recipients(int $campaign_id, ?int $data_source_id): array {
        if ($data_source_id === null) {
            // Fall back to manual recipients from email_campaign_recipients table
            return $this->get_manual_recipients_from_table($campaign_id);
        }

        try {
            // Get data from the configured data source
            $data_result = \system\data_source_manager::get_data_source_data($data_source_id);

            if (!$data_result['success']) {
                throw new Exception('Failed to get data from data source: ' . ($data_result['error'] ?? 'Unknown error'));
            }

            $recipients = [];
            $data_source = \system\data_source_manager::get_data_source($data_source_id);

            foreach ($data_result['data'] as $row) {
                // Try to find email field - common field names
                $email = $this->extract_email_from_row($row);
                if (empty($email)) {
                    continue; // Skip rows without valid email
                }

                // Try to find name field - common field names
                $name = $this->extract_name_from_row($row);

                $recipients[] = [
                    'email' => $email,
                    'name' => $name,
                    'data' => $row,
                    'rule' => 'data_source'
                ];
            }

            return $recipients;

        } catch (Exception $e) {
            tcs_log("Error getting recipients from data source $data_source_id: " . $e->getMessage(), "email_campaign");
            // Fall back to manual recipients if data source fails
            return $this->get_manual_recipients_from_table($campaign_id);
        }
    }

    /**
     * Get manual recipients from email_campaign_recipients table
     *
     * @param int $campaign_id Campaign ID
     * @return array Recipients list
     */
    private function get_manual_recipients_from_table(int $campaign_id): array {
        $recipients = $this->get_recipients($campaign_id, ['unsubscribed' => 0]);

        $result = [];
        foreach ($recipients as $recipient) {
            $result[] = [
                'email' => $recipient['email'],
                'name' => $recipient['name'],
                'data' => json_decode($recipient['metadata'] ?? '{}', true),
                'rule' => 'manual'
            ];
        }
        return $result;
    }

    /**
     * Extract email address from data row
     *
     * @param array $row Data row
     * @return string|null Email address
     */
    private function extract_email_from_row(array $row): ?string {
        // Common email field names to check
        $email_fields = [
            'email', 'email_address', 'primary_email', 'contact_email',
            'endcust_primary_admin_email', 'admin_email', 'user_email'
        ];

        foreach ($email_fields as $field) {
            if (!empty($row[$field]) && filter_var($row[$field], FILTER_VALIDATE_EMAIL)) {
                return $row[$field];
            }
        }

        return null;
    }

    /**
     * Extract name from data row
     *
     * @param array $row Data row
     * @return string Name
     */
    private function extract_name_from_row(array $row): string {
        // Try to build name from first/last name fields
        $first_name_fields = ['first_name', 'fname', 'endcust_primary_admin_first_name', 'admin_first_name'];
        $last_name_fields = ['last_name', 'lname', 'endcust_primary_admin_last_name', 'admin_last_name'];

        $first_name = '';
        $last_name = '';

        foreach ($first_name_fields as $field) {
            if (!empty($row[$field])) {
                $first_name = $row[$field];
                break;
            }
        }

        foreach ($last_name_fields as $field) {
            if (!empty($row[$field])) {
                $last_name = $row[$field];
                break;
            }
        }

        if ($first_name || $last_name) {
            return trim($first_name . ' ' . $last_name);
        }

        // Try single name fields
        $name_fields = ['name', 'full_name', 'display_name', 'contact_name', 'endCustomerName'];
        foreach ($name_fields as $field) {
            if (!empty($row[$field])) {
                return $row[$field];
            }
        }

        return 'Unknown';
    }

    /**
     * Send individual campaign email
     *
     * @param array $campaign Campaign data
     * @param array $template Template data
     * @param array $recipient Recipient data
     * @return array Send result
     */
    public function send_campaign_email(array $campaign, array $template, array $recipient): array {
        try {
            // Replace placeholders in template and subject
            $email_body = $this->replace_placeholders($template['body_template'], $recipient['data']);
            $subject = $this->replace_placeholders($campaign['subject_template'], $recipient['data']);

            // Record send attempt
            $history_id = $this->record_send_history([
                'campaign_id' => $campaign['id'],
                'template_id' => $template['id'],
                'recipient_email' => $recipient['email'],
                'recipient_name' => $recipient['name'],
                'recipient_type' => 'data_source',
                'recipient_reference_id' => $this->extract_reference_id($recipient['data']),
                'subject' => $subject,
                'body_preview' => substr(strip_tags($email_body), 0, 500),
                'triggered_by_rule' => $recipient['rule']
            ]);

            // Send email using generic email sending
            $send_result = $this->send_email(
                $recipient['email'],
                $campaign['from_email'],
                $subject,
                $email_body
            );

            // Update send status
            if ($send_result['success']) {
                $this->update_send_status($history_id, 'sent', $send_result['message']);
                return ['success' => true, 'message' => $send_result['message']];
            } else {
                $this->update_send_status($history_id, 'failed', $send_result['error']);
                return ['success' => false, 'error' => $send_result['error']];
            }

        } catch (Exception $e) {
            if (isset($history_id)) {
                $this->update_send_status($history_id, 'failed', $e->getMessage());
            }
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Replace placeholders in template content
     *
     * @param string $template Template content
     * @param array $data Data for placeholder replacement
     * @return string Processed template
     */
    private function replace_placeholders(string $template, array $data): string {
        if (empty($template)) {
            return '';
        }

        // Find all {{placeholder}} patterns and replace them
        return preg_replace_callback('/\{\{([^}]+)\}\}/', function($matches) use ($data) {
            $placeholder = trim($matches[1]);

            // Handle conditional placeholders like {{status,"active":"inactive"}}
            if (strpos($placeholder, ',') !== false) {
                $parts = explode(',', $placeholder, 2);
                $field = trim($parts[0]);
                $conditions = trim($parts[1], '"');

                if (isset($data[$field])) {
                    // Simple conditional replacement - can be extended for more complex logic
                    $condition_parts = explode(':', $conditions);
                    if (count($condition_parts) >= 2) {
                        $true_value = trim($condition_parts[0], '"');
                        $false_value = trim($condition_parts[1], '"');
                        return !empty($data[$field]) ? $true_value : $false_value;
                    }
                }
                return $matches[0]; // Return original if can't process
            }

            // Simple placeholder replacement
            return $data[$placeholder] ?? $matches[0];
        }, $template);
    }

    /**
     * Extract reference ID from recipient data
     *
     * @param array $data Recipient data
     * @return string|null Reference ID
     */
    private function extract_reference_id(array $data): ?string {
        // Common reference ID field names
        $ref_fields = [
            'id', 'reference_id', 'subscription_reference_number',
            'subs_subscriptionReferenceNumber', 'subscriptionReferenceNumber',
            'opportunity_number', 'opportunityNumber', 'ticket_id'
        ];

        foreach ($ref_fields as $field) {
            if (!empty($data[$field])) {
                return (string) $data[$field];
            }
        }

        return null;
    }

    /**
     * Send email using available email infrastructure
     *
     * @param string $to Recipient email
     * @param string $from Sender email
     * @param string $subject Email subject
     * @param string $body Email body
     * @return array Send result
     */
    private function send_email(string $to, string $from, string $subject, string $body): array {
        try {
            // Try to use existing email infrastructure if the file exists and class is available
            $autodesk_api_file = FS_APP_ROOT . '/resources/classes/autodesk_api.class.php';
            if (file_exists($autodesk_api_file) && class_exists('\autodesk_api\autodesk_api', false)) {
                try {
                    require_once $autodesk_api_file;
                    $autodesk = new \autodesk_api\autodesk_api();

                    $result = $autodesk->subscriptions->send_email($to, $from, $subject, $body);

                    if (strpos($result, 'Sent') !== false) {
                        return ['success' => true, 'message' => $result];
                    } else {
                        return ['success' => false, 'error' => $result];
                    }
                } catch (Exception $e) {
                    // If autodesk API fails, fall through to PHP mail
                    tcs_log("Autodesk API email failed, falling back to PHP mail: " . $e->getMessage(), "email_campaign");
                }
            }

            // Use PHP mail() function as primary method
            $headers = [
                'From: ' . $from,
                'Reply-To: ' . $from,
                'Content-Type: text/html; charset=UTF-8',
                'MIME-Version: 1.0'
            ];

            if (mail($to, $subject, $body, implode("\r\n", $headers))) {
                return ['success' => true, 'message' => 'Email sent successfully using PHP mail()'];
            } else {
                return ['success' => false, 'error' => 'Failed to send email using PHP mail()'];
            }

        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Email sending failed: ' . $e->getMessage()];
        }
    }

    /**
     * Check if campaign should send now based on schedule
     *
     * @param array $campaign Campaign data
     * @return bool Whether to send now
     */
    public function should_send_now(array $campaign): bool {
        $send_schedule = $campaign['send_schedule'] ?? [];

        // Check day of week
        $current_day = (int) date('w'); // 0 = Sunday, 6 = Saturday
        $send_days = $send_schedule['send_days'] ?? [0, 1, 1, 1, 1, 1, 0]; // Default: Mon-Fri

        if (!$send_days[$current_day]) {
            return false;
        }

        // Check time of day
        $current_hour = (int) date('H');
        $send_time = $send_schedule['send_time'] ?? 9;

        // Allow sending within 1 hour window
        if (abs($current_hour - $send_time) > 1) {
            return false;
        }

        return true;
    }

    /**
     * Generate unsubscribe token for recipient
     *
     * @param string $email Recipient email
     * @param int $campaign_id Campaign ID
     * @return string Unsubscribe token
     */
    public function generate_unsubscribe_token(string $email, int $campaign_id): string {
        $token = bin2hex(random_bytes(32));
        $expires_at = date('Y-m-d H:i:s', strtotime('+1 year'));

        database::table('email_unsubscribe_tokens')->insert([
            'token' => $token,
            'email' => $email,
            'campaign_id' => $campaign_id,
            'expires_at' => $expires_at
        ]);

        return $token;
    }

    /**
     * Process unsubscribe request
     *
     * @param string $token Unsubscribe token
     * @return array Result with success status and message
     */
    public function process_unsubscribe(string $token): array {
        $token_record = database::table('email_unsubscribe_tokens')
            ->where('token', $token)
            ->where('expires_at', '>', date('Y-m-d H:i:s'))
            ->where('used_at', null)
            ->first();

        if (!$token_record) {
            return ['success' => false, 'message' => 'Invalid or expired unsubscribe token'];
        }

        try {
            // Mark token as used
            database::table('email_unsubscribe_tokens')
                ->where('id', $token_record['id'])
                ->update(['used_at' => date('Y-m-d H:i:s')]);

            // Unsubscribe from campaign
            if ($token_record['campaign_id']) {
                database::table('email_campaign_recipients')
                    ->where('campaign_id', $token_record['campaign_id'])
                    ->where('email', $token_record['email'])
                    ->update([
                        'unsubscribed' => 1,
                        'unsubscribed_at' => date('Y-m-d H:i:s')
                    ]);
            }

            // Log the unsubscribe action
            tcs_log("User unsubscribed from campaign {$token_record['campaign_id']}: {$token_record['email']}", "email_campaign");

            return ['success' => true, 'message' => 'Successfully unsubscribed from email campaign'];

        } catch (Exception $e) {
            return ['success' => false, 'message' => 'Failed to process unsubscribe: ' . $e->getMessage()];
        }
    }
}
