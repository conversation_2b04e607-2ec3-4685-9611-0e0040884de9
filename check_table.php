<?php
// Direct MySQLi connection
$tcs_database = 'wwwcadservicescouk';
$db_server = 'localhost';
$db_username = $tcs_database;
$db_password = 'S96#1kvYuCGE';
$db_database = $tcs_database;

try {
    $mysqli = new mysqli($db_server, $db_username, $db_password, $db_database);

    if ($mysqli->connect_error) {
        die("Connection failed: " . $mysqli->connect_error . "\n");
    }

    // Check if table exists
    $result = $mysqli->query("SHOW TABLES LIKE 'email_campaign_history'");
    $table_exists = $result && $result->num_rows > 0;

    if (!$table_exists) {
        echo "Table email_campaign_history does not exist!\n";

        // Show all tables that contain 'email'
        $result = $mysqli->query("SHOW TABLES LIKE '%email%'");
        echo "Tables containing 'email':\n";
        if ($result) {
            while ($row = $result->fetch_array()) {
                echo "- " . $row[0] . "\n";
            }
        }
    } else {
        echo "Table email_campaign_history exists.\n";

        // Get table structure
        $result = $mysqli->query('DESCRIBE email_campaign_history');
        echo "Table structure:\n";
        if ($result) {
            while ($row = $result->fetch_assoc()) {
                echo "- " . $row['Field'] . " (" . $row['Type'] . ")\n";
            }
        }
    }

    $mysqli->close();

} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
